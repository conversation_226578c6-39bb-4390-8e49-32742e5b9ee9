from uuid import UUI<PERSON>

from pydantic import BaseModel

from onyx.db.models import UserGroup as UserGroupModel
from onyx.auth.schemas import UserRole
from onyx.server.documents.models import ConnectorCredentialPairDescriptor
from onyx.server.documents.models import Con<PERSON><PERSON><PERSON><PERSON>shot
from onyx.server.documents.models import CredentialSnapshot
from onyx.server.features.document_set.models import DocumentSet
from onyx.server.features.persona.models import PersonaSnapshot
from onyx.server.manage.models import UserInfo
from onyx.server.manage.models import UserPreferences



class UserTeams(BaseModel):
    id: int
    name: str
    users: list[UserInfo]
    is_up_to_date: bool
    is_up_for_deletion: bool

    @classmethod
    def from_model(cls, user_group_model: UserGroupModel) -> "UserTeams":
        return cls(
            id=user_group_model.id,
            name=user_group_model.name,
            users=[
                UserInfo(
                    id=str(user.id),
                    email=user.email,
                    is_active=user.is_active,
                    is_superuser=user.is_superuser,
                    is_verified=user.is_verified,
                    role=user.role,
                    is_admin= user.is_admin,
                    preferences=UserPreferences(
                        default_model=user.default_model,
                        chosen_assistants=user.chosen_assistants,
                    ),
                )
                for user in user_group_model.users
            ],
            is_up_to_date=user_group_model.is_up_to_date,
            is_up_for_deletion=user_group_model.is_up_for_deletion,
        )


class UserTeamMember(BaseModel):
    user_id: UUID
    role: UserRole = UserRole.BASIC  # Default role for team members


class CreateUserTeams(BaseModel):
    name: str
    user_ids: list[UUID] | None = None  # For backward compatibility
    members: list[UserTeamMember] | None = None  # New way with roles


class UpdateUserTeams(BaseModel):
    name: str
    user_ids: list[UUID] | None = None  # For backward compatibility
    members: list[UserTeamMember] | None = None  # New way with roles
